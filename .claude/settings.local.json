{
  "permissions": {
    "allow": [
      "Bash(task lint)",
      "<PERSON>sh(task check)",
      "<PERSON>sh(npm run type-check)",
      "Bash(task test:run)",
      "Bash(npm install --prefix packages/web @testing-library/react --save-dev)",
      "Bash(task build)",
      "Bash(mkdir -p packages/docs/commands)",
      "Bash(task check --apply)",
      "<PERSON>sh(npx biome check --apply .)",
      "Bash(npm run check:fix)"
    ],
    "deny": []
  }
}

{
  "permissions": {
    "allow": [
      "Bash(task lint)",
      "Bash(task check)",
      "Bash(npm run type-check)",
      "Bash(task test:run)",
      "Bash(npm install --prefix packages/web @testing-library/react --save-dev)",
      "Bash(task build)",
      "Bash(mkdir -p packages/docs/commands)",
      "Bash(task check --apply)",
      "Bash(npx biome check --apply .)",
      "<PERSON>sh(npm run check:fix)",
      "Bash(task setup)"
    ],
    "deny": []
  }
}
