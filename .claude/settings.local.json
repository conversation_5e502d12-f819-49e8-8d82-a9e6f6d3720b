{"permissions": {"allow": ["<PERSON><PERSON>(task lint)", "<PERSON><PERSON>(task check)", "Bash(npm run type-check)", "<PERSON><PERSON>(task test:run)", "Bash(npm install --prefix packages/web @testing-library/react --save-dev)", "<PERSON>sh(task build)", "Bash(mkdir -p packages/docs/commands)", "<PERSON><PERSON>(task check --apply)", "Bash(npx biome check --apply .)", "Bash(npm run check:fix)", "<PERSON><PERSON>(task setup)"], "deny": []}}