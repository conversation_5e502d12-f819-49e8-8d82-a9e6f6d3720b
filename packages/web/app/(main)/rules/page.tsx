'use client';

import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  Dialog,
  Flex,
  Heading,
  Select,
  Text,
  TextField,
} from '@radix-ui/themes';
import {
  Code,
  Download,
  Eye,
  FileText,
  Filter,
  Lock,
  Plus,
  Search,
  Upload,
  Users,
} from 'lucide-react';
import { useMemo, useState } from 'react';
import { toast } from 'sonner';
import { RuleCard } from '@/components/rule-card';
import { RuleEditor } from '@/components/rule-editor';
import RuleUploadDropzone from '@/components/rule-upload-dropzone';
import {
  useCreateRule,
  useDeleteRule,
  useInvalidateRules,
  useRules,
  useUpdateRule,
} from '@/hooks/use-rule-queries';
import { useTags } from '@/hooks/use-tag-queries';
import { useSession } from '@/lib/auth-client';
import type { Rule, RulePayload } from '@/lib/store';

export const dynamic = 'force-dynamic';

export default function RulesPage() {
  const { data: session } = useSession();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTags, setSelectedTags] = useState<string[]>([]);
  const [selectedVisibility, setSelectedVisibility] = useState('ALL');
  const [showEditor, setShowEditor] = useState(false);
  const [editingRule, setEditingRule] = useState<Rule | null>(null);
  const [showImportDialog, setShowImportDialog] = useState(false);

  // Memoize the filters object
  const ruleFilters = useMemo(
    () => ({
      search: searchQuery || undefined,
      tags: selectedTags.length > 0 ? selectedTags : undefined,
      visibility: selectedVisibility !== 'ALL' ? selectedVisibility : undefined,
    }),
    [searchQuery, selectedTags, selectedVisibility]
  );

  // Use query hooks
  const { data: rulesResponse, isLoading: rulesLoading } = useRules(ruleFilters);
  const { data: tags = [] } = useTags();

  // Mutation hooks
  const createRuleMutation = useCreateRule();
  const updateRuleMutation = useUpdateRule();
  const deleteRuleMutation = useDeleteRule();
  const invalidateRules = useInvalidateRules();

  // Extract rules from response
  const rules = rulesResponse?.rules || [];
  const userRules = rules.filter((rule) => rule.userId === session?.user?.id);

  const loading = rulesLoading;
  const isSaving = createRuleMutation.isPending || updateRuleMutation.isPending;

  // Statistics
  const stats = {
    totalRules: userRules.length,
    publicRules: userRules.filter((r) => r.visibility === 'PUBLIC').length,
    privateRules: userRules.filter((r) => r.visibility === 'PRIVATE').length,
    taggedRules: userRules.filter((r) => r.tags.length > 0).length,
  };

  const handleCreateRule = () => {
    setEditingRule(null);
    setShowEditor(true);
  };

  const handleEditRule = (rule: Rule) => {
    setEditingRule(rule);
    setShowEditor(true);
  };

  const handleSaveRule = async (payload: Partial<RulePayload>) => {
    try {
      if (editingRule) {
        // Update existing rule
        const response = await fetch(`/api/rules/${editingRule.id}`, {
          method: 'PUT',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(payload),
        });

        if (response.ok) {
          toast.success('Rule updated');
          setShowEditor(false);
          setEditingRule(null);
          invalidateRules();
        } else {
          toast.error('Failed to update rule');
        }
      } else {
        // Create new rule
        const response = await fetch('/api/rules', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(payload),
        });

        if (response.ok) {
          toast.success('Rule created');
          setShowEditor(false);
          setEditingRule(null);
          invalidateRules();
        } else {
          toast.error('Failed to create rule');
        }
      }
    } catch (error) {
      console.error('Error saving rule:', error);
      toast.error('Failed to save rule');
    }
  };

  const handleDeleteRule = async (ruleId: string) => {
    try {
      await deleteRuleMutation.mutateAsync(ruleId);
    } catch (error) {
      console.error('Error deleting rule:', error);
    }
  };

  const toggleTag = (tagName: string) => {
    setSelectedTags((prev) =>
      prev.includes(tagName) ? prev.filter((t) => t !== tagName) : [...prev, tagName]
    );
  };

  const handleExportRules = () => {
    const exportData = {
      rules: userRules.map((rule) => ({
        title: rule.title,
        description: rule.description,
        content: rule.content,
        tags: rule.tags.map((t) => t.tag.name),
        visibility: rule.visibility,
        applyType: rule.applyType,
      })),
      exportedAt: new Date().toISOString(),
      version: '1.0',
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], {
      type: 'application/json',
    });

    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `rules-export-${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast.success(`Exported ${userRules.length} rules`);
  };

  const handleImportRules = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    try {
      const text = await file.text();
      const data = JSON.parse(text);

      if (!data.rules || !Array.isArray(data.rules)) {
        toast.error('Invalid import file format');
        return;
      }

      // Import rules one by one
      let imported = 0;
      for (const rule of data.rules) {
        try {
          await fetch('/api/rules', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({
              title: rule.title,
              description: rule.description,
              content: rule.content,
              tags: rule.tags,
              visibility: rule.visibility || 'PRIVATE',
              applyType: rule.applyType || 'PROJECT',
            }),
          });
          imported++;
        } catch (error) {
          console.error('Failed to import rule:', rule.title, error);
        }
      }

      toast.success(`Imported ${imported} of ${data.rules.length} rules`);
      invalidateRules();
      setShowImportDialog(false);
    } catch (error) {
      console.error('Import error:', error);
      toast.error('Failed to import rules');
    }
  };

  if (!session?.user) {
    return (
      <Box className="container py-8">
        <div className="text-center">
          <Heading size="6" weight="bold" className="mb-4">
            Please sign in to continue
          </Heading>
          <Button asChild>
            <a href="/auth/signin">Sign In</a>
          </Button>
        </div>
      </Box>
    );
  }

  if (loading && !rules.length) {
    return (
      <Box className="mobile-container space-y-6 xs:space-y-8 py-6 xs:py-8">
        <div className="text-center">
          <Heading size="6" weight="bold" className="text-2xl xs:text-3xl">
            My Rules
          </Heading>
          <Text size="2" color="gray" className="mb-8">
            Loading your rules...
          </Text>
        </div>
      </Box>
    );
  }

  return (
    <Box className="mobile-container space-y-6 xs:space-y-8 py-6 xs:py-8">
      {/* Header */}
      <Flex
        justify="between"
        align="center"
        direction={{ initial: 'column', xs: 'row' }}
        gap={{ initial: '4', xs: '0' }}
      >
        <Box className="xs:text-left text-center">
          <Heading size="6" xs-size="8" weight="bold" className="text-2xl xs:text-3xl">
            My Rules
          </Heading>
          <Text size="2" xs-size="3" color="gray" className="text-sm xs:text-base">
            Manage your AI prompt rules and configurations
          </Text>
        </Box>
        <Flex gap="2" className="w-full xs:w-auto">
          <Button onClick={handleCreateRule} className="flex-1 xs:flex-initial">
            <Plus className="mr-2 h-4 w-4" />
            New Rule
          </Button>
          <Button variant="soft" onClick={handleExportRules} disabled={userRules.length === 0}>
            <Download className="h-4 w-4" />
          </Button>
          <Button variant="soft" onClick={() => setShowImportDialog(true)}>
            <Upload className="h-4 w-4" />
          </Button>
        </Flex>
      </Flex>

      {/* Stats */}
      <Box className="grid grid-cols-2 gap-3 xs:gap-4 md:grid-cols-4">
        <Card className="mobile-card">
          <Flex justify="between" align="center" pb="2">
            <Text size="1" weight="medium" color="gray">
              Total Rules
            </Text>
            <Code className="h-3 xs:h-4 w-3 xs:w-4" style={{ color: 'var(--gray-11)' }} />
          </Flex>
          <Text size="5" weight="bold">
            {stats.totalRules}
          </Text>
        </Card>

        <Card className="mobile-card">
          <Flex justify="between" align="center" pb="2">
            <Text size="1" weight="medium" color="gray">
              Public
            </Text>
            <Users className="h-3 xs:h-4 w-3 xs:w-4" style={{ color: 'var(--gray-11)' }} />
          </Flex>
          <Text size="5" weight="bold">
            {stats.publicRules}
          </Text>
        </Card>

        <Card className="mobile-card">
          <Flex justify="between" align="center" pb="2">
            <Text size="1" weight="medium" color="gray">
              Private
            </Text>
            <Lock className="h-3 xs:h-4 w-3 xs:w-4" style={{ color: 'var(--gray-11)' }} />
          </Flex>
          <Text size="5" weight="bold">
            {stats.privateRules}
          </Text>
        </Card>

        <Card className="mobile-card">
          <Flex justify="between" align="center" pb="2">
            <Text size="1" weight="medium" color="gray">
              Tagged
            </Text>
            <Filter className="h-3 xs:h-4 w-3 xs:w-4" style={{ color: 'var(--gray-11)' }} />
          </Flex>
          <Text size="5" weight="bold">
            {stats.taggedRules}
          </Text>
        </Card>
      </Box>

      {/* Filters */}
      <Card className="mobile-card space-y-4">
        <Flex direction={{ initial: 'column', sm: 'row' }} gap="3">
          <TextField.Root
            placeholder="Search rules..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="flex-1"
          >
            <TextField.Slot>
              <Search className="h-4 w-4" />
            </TextField.Slot>
          </TextField.Root>

          <Select.Root value={selectedVisibility} onValueChange={setSelectedVisibility}>
            <Select.Trigger placeholder="Visibility" />
            <Select.Content>
              <Select.Item value="ALL">All Rules</Select.Item>
              <Select.Item value="PUBLIC">Public Only</Select.Item>
              <Select.Item value="PRIVATE">Private Only</Select.Item>
            </Select.Content>
          </Select.Root>
        </Flex>

        {tags.length > 0 && (
          <Flex wrap="wrap" gap="2">
            {tags.map((tag) => (
              <Badge
                key={tag.id}
                variant={selectedTags.includes(tag.name) ? 'solid' : 'outline'}
                style={{
                  borderColor: tag.color,
                  backgroundColor: selectedTags.includes(tag.name) ? tag.color : undefined,
                  cursor: 'pointer',
                }}
                onClick={() => toggleTag(tag.name)}
              >
                {tag.name}
              </Badge>
            ))}
          </Flex>
        )}
      </Card>

      {/* Create from Markdown */}
      <Card className="mobile-card space-y-3">
        <Flex align="center" gap="2">
          <FileText className="h-4 w-4" />
          <Heading size="3">Create from Markdown</Heading>
        </Flex>
        <Text size="2" color="gray">
          Drag and drop .md/.mdx files to quickly add rules. We will parse title, description, and
          glob patterns.
        </Text>
        <RuleUploadDropzone
          onCompleted={(n) => {
            if (n && n > 0) invalidateRules();
          }}
        />
      </Card>

      {/* Rules List */}
      {userRules.length === 0 ? (
        <Card className="mobile-card py-12 text-center">
          <Eye className="mx-auto mb-4 h-12 w-12 text-gray-400" />
          <Heading size="4" className="mb-2">
            No rules found
          </Heading>
          <Text color="gray" className="mb-4">
            {searchQuery || selectedTags.length > 0 || selectedVisibility !== 'ALL'
              ? 'Try adjusting your filters'
              : 'Create your first rule to get started'}
          </Text>
          {!searchQuery && selectedTags.length === 0 && selectedVisibility === 'ALL' && (
            <Button onClick={handleCreateRule}>
              <Plus className="mr-2 h-4 w-4" />
              Create Rule
            </Button>
          )}
        </Card>
      ) : (
        <div className="space-y-4">
          {userRules.map((rule) => (
            <RuleCard
              key={rule.id}
              rule={rule}
              onEdit={handleEditRule}
              onDelete={handleDeleteRule}
              isOwner={true}
            />
          ))}
        </div>
      )}

      {/* Rule Editor Dialog */}
      <Dialog.Root open={showEditor} onOpenChange={setShowEditor}>
        <Dialog.Content className="max-h-[90vh] w-[95vw] max-w-5xl overflow-hidden">
          <RuleEditor
            rule={editingRule ?? undefined}
            onSave={handleSaveRule}
            onCancel={() => {
              setShowEditor(false);
              setEditingRule(null);
            }}
            isLoading={isSaving}
          />
        </Dialog.Content>
      </Dialog.Root>

      {/* Import Dialog */}
      <Dialog.Root open={showImportDialog} onOpenChange={setShowImportDialog}>
        <Dialog.Content className="max-w-md">
          <Dialog.Title>Import Rules</Dialog.Title>
          <Dialog.Description>
            Select a JSON file exported from this app to import rules.
          </Dialog.Description>
          <Box className="mt-4">
            <input
              type="file"
              accept=".json"
              onChange={handleImportRules}
              className="block w-full text-sm file:mr-4 file:rounded-md file:border-0 file:bg-primary file:px-4 file:py-2 file:font-semibold file:text-primary-foreground file:text-sm hover:file:bg-primary/90"
            />
          </Box>
          <Flex gap="3" mt="4" justify="end">
            <Button variant="soft" onClick={() => setShowImportDialog(false)}>
              Cancel
            </Button>
          </Flex>
        </Dialog.Content>
      </Dialog.Root>
    </Box>
  );
}
