import { <PERSON>, But<PERSON>, Container, <PERSON>lex, Heading, Text } from '@radix-ui/themes';
import type { Metadata } from 'next';
import Link from 'next/link';
import { CommandRuleSection } from '@/components/home/<USER>';
import { FeaturedRulesSectionStatic } from '@/components/home/<USER>';
import { StaticNavbar } from '@/components/layout/static-navbar';
import { getCommandRule } from '@/lib/docs-command-normalizer';

export const metadata: Metadata = {
  title: 'OnlyRules - AI Prompt Management Platform | Boost Your Coding Productivity',
  description:
    'Create, organize, and share AI prompt rules for your favorite IDEs. Boost your coding productivity with community-driven templates for Cursor, Windsurf, Claude, GitHub Copilot, and more.',
  keywords:
    'AI prompts, IDE productivity, coding rules, Cursor IDE, Windsurf, Claude, GitHub Copilot, prompt engineering, developer tools, AI coding assistant',
  openGraph: {
    title: 'OnlyRules - AI Prompt Management Platform',
    description:
      'Create, organize, and share AI prompt rules for your favorite IDEs. Boost your coding productivity with community-driven templates.',
    type: 'website',
    url: '/',
    siteName: 'OnlyRules',
    images: [
      {
        url: '/og-image.png',
        width: 1200,
        height: 630,
        alt: 'OnlyRules - AI Prompt Management Platform',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'OnlyRules - AI Prompt Management Platform',
    description: 'Create, organize, and share AI prompt rules for your favorite IDEs.',
    images: ['/og-image.png'],
  },
  alternates: {
    canonical: '/',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
};

export default function HomePage() {
  const commandRule = getCommandRule();
  const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'https://onlyrules.app';

  const structuredData = {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: 'OnlyRules',
    description:
      'AI Prompt Management Platform for developers. Create, organize, and share AI prompt rules for your favorite IDEs.',
    url: baseUrl,
    potentialAction: {
      '@type': 'SearchAction',
      target: {
        '@type': 'EntryPoint',
        urlTemplate: `${baseUrl}/search?q={search_term_string}`,
      },
      'query-input': 'required name=search_term_string',
    },
    publisher: {
      '@type': 'Organization',
      name: 'OnlyRules',
      url: baseUrl,
      logo: {
        '@type': 'ImageObject',
        url: `${baseUrl}/logo.png`,
      },
    },
    mainEntity: {
      '@type': 'SoftwareApplication',
      name: 'OnlyRules',
      applicationCategory: 'DeveloperApplication',
      operatingSystem: 'Web',
      description:
        'AI Prompt Management Platform for developers. Create, organize, and share AI prompt rules for your favorite IDEs including Cursor, Windsurf, Claude, GitHub Copilot, and more.',
      offers: {
        '@type': 'Offer',
        price: '0',
        priceCurrency: 'USD',
      },
    },
  };

  return (
    <>
      {/* Structured Data for SEO */}
      <script
        type="application/ld+json"
        // biome-ignore lint/security/noDangerouslySetInnerHtml: Used to inject SEO-related JSON-LD structured data.
        dangerouslySetInnerHTML={{ __html: JSON.stringify(structuredData) }}
      />

      <StaticNavbar />

      {/* Hero Section */}
      <Box className="flex items-center py-16 md:py-24">
        <Container size="3" px={{ initial: '4', xs: '6' }}>
          <Flex
            direction="column"
            align="center"
            gap={{ initial: '6', xs: '8', md: '9' }}
            py={{ initial: '8', xs: '9', md: '9' }}
            className="text-center"
          >
            {/* Hero Title */}
            <Heading
              size={{ initial: '8', xs: '9', md: '9' }}
              weight="bold"
              className="tracking-tight"
            >
              OnlyRules
            </Heading>

            {/* Hero Description */}
            <Text size={{ initial: '4', xs: '5', md: '5' }} color="gray" className="max-w-2xl">
              AI Prompt Management Platform for developers. Create, organize, and share AI prompt
              rules for your favorite IDEs.
            </Text>

            {/* Action Buttons */}
            <Flex
              direction={{ initial: 'column', sm: 'row' }}
              gap={{ initial: '3', xs: '4' }}
              mt={{ initial: '4', xs: '6' }}
              width={{ initial: '100%', xs: 'auto' }}
            >
              <Button size={{ initial: '3', xs: '3' }} variant="soft" asChild>
                <Link href="/auth/signin">Get Started</Link>
              </Button>

              <Button size={{ initial: '3', xs: '3' }} variant="outline" asChild>
                <Link href="/templates">Browse Templates</Link>
              </Button>
            </Flex>
          </Flex>
        </Container>
      </Box>

      {/* Featured Rules Section */}
      <div>
        <FeaturedRulesSectionStatic />
        <CommandRuleSection rule={commandRule} />
      </div>
    </>
  );
}
