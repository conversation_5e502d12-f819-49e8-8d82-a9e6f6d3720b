'use client';

import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>,
  Card,
  Container,
  Dialog,
  DropdownMenu,
  Flex,
  Heading,
  Separator,
  Text,
} from '@radix-ui/themes';
import { formatDistanceToNow } from 'date-fns';
import {
  ArrowLeft,
  Calendar,
  Copy,
  Download,
  Edit,
  ExternalLink,
  Eye,
  Lock,
  MoreVertical,
  Package,
  Share2,
  Trash2,
  User,
} from 'lucide-react';
import { useParams, useRouter, useSearchParams } from 'next/navigation';
import { useEffect, useState } from 'react';
import { toast } from 'sonner';
import { RulesetContentViewer } from '@/components/ruleset-content-viewer';
import { RulesetForm } from '@/components/ruleset-form';
import { RulesetRuleNavigation } from '@/components/ruleset-rule-navigation';
import { ResizableHandle, ResizablePanel, ResizablePanelGroup } from '@/components/ui/resizable';
import {
  type CreateRulesetData,
  type UpdateRulesetData,
  useDeleteRuleset,
  useRuleset,
  useUpdateRuleset,
} from '@/hooks/use-ruleset-queries';
import { useSession } from '@/lib/auth-client';

// Force dynamic rendering to avoid build-time database issues
export const dynamic = 'force-dynamic';

function RulesetPageContent({
  rulesetId,
  shareToken,
}: {
  rulesetId: string;
  shareToken: string | null;
}) {
  const router = useRouter();
  const { data: session } = useSession();

  const [showEditDialog, setShowEditDialog] = useState(false);
  const [showShareDialog, setShowShareDialog] = useState(false);
  const [selectedRuleId, setSelectedRuleId] = useState<string | 'ALL'>('ALL');

  // Fetch ruleset data
  const { data: ruleset, isLoading, error } = useRuleset(rulesetId, shareToken || undefined);

  // Mutation hooks
  const updateRulesetMutation = useUpdateRuleset();
  const deleteRulesetMutation = useDeleteRuleset();

  // Check if user is the owner
  const isOwner = session?.user?.id === ruleset?.user.id;
  const isPublic = ruleset?.visibility === 'PUBLIC';

  useEffect(() => {
    if (error) {
      toast.error('Failed to load ruleset');
      router.push('/rulesets');
    }
  }, [error, router]);

  const handleEdit = () => {
    setShowEditDialog(true);
  };

  const handleDelete = async () => {
    if (!ruleset) return;

    if (
      window.confirm(
        `Are you sure you want to delete "${ruleset.name}"? This action cannot be undone.`
      )
    ) {
      try {
        await deleteRulesetMutation.mutateAsync(ruleset.id);
        toast.success('Ruleset deleted successfully');
        router.push('/rulesets');
      } catch (error) {
        console.error('Error deleting ruleset:', error);
      }
    }
  };

  const handleShare = () => {
    setShowShareDialog(true);
  };

  const handleCopyShareLink = async () => {
    if (!ruleset?.shareToken) return;

    const shareUrl = `${window.location.origin}/rulesets/${ruleset.id}?token=${ruleset.shareToken}`;

    try {
      await navigator.clipboard.writeText(shareUrl);
      toast.success('Share link copied to clipboard');
    } catch (error) {
      console.error('Failed to copy link:', error);
      toast.error('Failed to copy link');
    }
  };

  const handleCopyRulesetId = async () => {
    if (!ruleset) return;

    try {
      await navigator.clipboard.writeText(ruleset.id);
      toast.success('Ruleset ID copied to clipboard');
    } catch (error) {
      console.error('Failed to copy ID:', error);
      toast.error('Failed to copy ID');
    }
  };

  const handleUpdateRuleset = async (data: CreateRulesetData | UpdateRulesetData) => {
    if (!('id' in data)) {
      return; // Should not happen on this page
    }
    try {
      await updateRulesetMutation.mutateAsync(data);
      setShowEditDialog(false);
    } catch (error) {
      console.error('Error updating ruleset:', error);
    }
  };

  const handleExportRuleset = () => {
    if (!ruleset) return;

    const exportData = {
      name: ruleset.name,
      description: ruleset.description,
      rules: ruleset.rules.map(({ rule }) => ({
        title: rule.title,
        description: rule.description,
        // Note: Content not included in export as it's not available in ruleset query
        tags: rule.tags.map(({ tag }) => tag.name),
      })),
      exportedAt: new Date().toISOString(),
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `${ruleset.name.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_ruleset.json`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast.success('Ruleset exported successfully');
  };

  if (isLoading) {
    return (
      <Container size="4" py="8">
        <Box className="text-center">
          <Heading size="6" mb="4">
            Loading Ruleset...
          </Heading>
          <div className="animate-pulse space-y-4">
            <div className="h-32 rounded-lg bg-gray-200 dark:bg-gray-700"></div>
            <div className="grid gap-4 md:grid-cols-2">
              <div className="h-24 rounded-lg bg-gray-200 dark:bg-gray-700"></div>
              <div className="h-24 rounded-lg bg-gray-200 dark:bg-gray-700"></div>
              <div className="h-24 rounded-lg bg-gray-200 dark:bg-gray-700"></div>
              <div className="h-24 rounded-lg bg-gray-200 dark:bg-gray-700"></div>
            </div>
          </div>
        </Box>
      </Container>
    );
  }

  if (!ruleset) {
    return (
      <Container size="4" py="8">
        <Box className="text-center">
          <Heading size="6" mb="4">
            Ruleset Not Found
          </Heading>
          <Text color="gray" mb="4">
            The ruleset you're looking for doesn't exist or you don't have permission to view it.
          </Text>
          <Button onClick={() => router.push('/rulesets')}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Rulesets
          </Button>
        </Box>
      </Container>
    );
  }

  return (
    <Container size="4" py="8">
      <Flex direction="column" gap="6">
        {/* Header */}
        <Flex justify="between" align="start" gap="4">
          <Button variant="ghost" onClick={() => router.push('/rulesets')}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back to Rulesets
          </Button>

          {isOwner && (
            <DropdownMenu.Root>
              <DropdownMenu.Trigger>
                <Button variant="ghost" size="2">
                  <MoreVertical className="h-4 w-4" />
                </Button>
              </DropdownMenu.Trigger>
              <DropdownMenu.Content>
                <DropdownMenu.Item onClick={handleEdit}>
                  <Edit className="mr-2 h-4 w-4" />
                  Edit Ruleset
                </DropdownMenu.Item>
                <DropdownMenu.Item onClick={handleExportRuleset}>
                  <Download className="mr-2 h-4 w-4" />
                  Export Ruleset
                </DropdownMenu.Item>
                {isPublic && (
                  <DropdownMenu.Item onClick={handleShare}>
                    <Share2 className="mr-2 h-4 w-4" />
                    Share Ruleset
                  </DropdownMenu.Item>
                )}
                <DropdownMenu.Separator />
                <DropdownMenu.Item onClick={handleDelete} color="red">
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete Ruleset
                </DropdownMenu.Item>
              </DropdownMenu.Content>
            </DropdownMenu.Root>
          )}
        </Flex>

        {/* Ruleset Info */}
        <Card>
          <Box p="6">
            <Flex justify="between" align="start" mb="4">
              <Box className="flex-1">
                <Heading size="7" mb="2">
                  {ruleset.name}
                </Heading>
                {ruleset.description && (
                  <Text size="3" color="gray" mb="4">
                    {ruleset.description}
                  </Text>
                )}
              </Box>
              <Badge variant="soft" color={isPublic ? 'green' : 'gray'} size="2">
                {isPublic ? (
                  <>
                    <Eye className="mr-1 h-3 w-3" />
                    Public
                  </>
                ) : (
                  <>
                    <Lock className="mr-1 h-3 w-3" />
                    Private
                  </>
                )}
              </Badge>
            </Flex>

            <Flex gap="6" wrap="wrap">
              <Flex align="center" gap="2">
                <User className="h-4 w-4 text-gray-500" />
                <Text size="2" color="gray">
                  Created by {ruleset.user.name || ruleset.user.email}
                </Text>
              </Flex>
              <Flex align="center" gap="2">
                <Calendar className="h-4 w-4 text-gray-500" />
                <Text size="2" color="gray">
                  Updated {formatDistanceToNow(new Date(ruleset.updatedAt), { addSuffix: true })}
                </Text>
              </Flex>
              <Flex align="center" gap="2">
                <Package className="h-4 w-4 text-gray-500" />
                <Text size="2" color="gray">
                  {ruleset.rules.length} rule{ruleset.rules.length !== 1 ? 's' : ''}
                </Text>
              </Flex>
            </Flex>

            {/* Quick Actions */}
            <Flex gap="3" mt="4">
              <Button variant="soft" onClick={handleCopyRulesetId}>
                <Copy className="mr-2 h-4 w-4" />
                Copy ID
              </Button>
              {isPublic && ruleset.shareToken && (
                <Button variant="soft" onClick={handleCopyShareLink}>
                  <ExternalLink className="mr-2 h-4 w-4" />
                  Copy Share Link
                </Button>
              )}
              <Button variant="soft" onClick={handleExportRuleset}>
                <Download className="mr-2 h-4 w-4" />
                Export
              </Button>
            </Flex>
          </Box>
        </Card>

        <Separator size="4" />

        {/* Rules Section */}
        <Box>
          <Heading size="5" mb="4">
            Rules in this Ruleset ({ruleset.rules.length})
          </Heading>

          {ruleset.rules.length === 0 ? (
            <Card className="py-12 text-center">
              <Package className="mx-auto mb-4 h-12 w-12 text-gray-400" />
              <Heading size="4" mb="2">
                No rules in this ruleset
              </Heading>
              <Text color="gray" mb="4">
                This ruleset doesn't contain any rules yet.
              </Text>
              {isOwner && (
                <Button onClick={handleEdit}>
                  <Edit className="mr-2 h-4 w-4" />
                  Add Rules
                </Button>
              )}
            </Card>
          ) : (
            <>
              {/* Desktop Layout - Two Column Resizable */}
              <Card className="hidden h-[70vh] overflow-hidden md:block">
                <ResizablePanelGroup direction="horizontal" className="h-full">
                  {/* Left Panel - Rule Navigation */}
                  <ResizablePanel defaultSize={30} minSize={25} maxSize={40}>
                    <RulesetRuleNavigation
                      rules={ruleset.rules.sort((a, b) => a.order - b.order)}
                      selectedRuleId={selectedRuleId}
                      onRuleSelect={setSelectedRuleId}
                    />
                  </ResizablePanel>

                  <ResizableHandle withHandle />

                  {/* Right Panel - Content Viewer */}
                  <ResizablePanel defaultSize={70} minSize={60}>
                    <RulesetContentViewer
                      rules={ruleset.rules.sort((a, b) => a.order - b.order)}
                      selectedRuleId={selectedRuleId}
                      rulesetName={ruleset.name}
                    />
                  </ResizablePanel>
                </ResizablePanelGroup>
              </Card>

              {/* Mobile Layout - Stacked */}
              <div className="space-y-4 md:hidden">
                <Card>
                  <RulesetRuleNavigation
                    rules={ruleset.rules.sort((a, b) => a.order - b.order)}
                    selectedRuleId={selectedRuleId}
                    onRuleSelect={setSelectedRuleId}
                  />
                </Card>
                <Card className="h-[60vh] overflow-hidden">
                  <RulesetContentViewer
                    rules={ruleset.rules.sort((a, b) => a.order - b.order)}
                    selectedRuleId={selectedRuleId}
                    rulesetName={ruleset.name}
                  />
                </Card>
              </div>
            </>
          )}
        </Box>

        {/* Edit Dialog */}
        <Dialog.Root open={showEditDialog} onOpenChange={setShowEditDialog}>
          <Dialog.Content className="max-h-[90vh] w-[95vw] max-w-5xl overflow-hidden">
            <Dialog.Title className="sr-only">Edit Ruleset</Dialog.Title>
            <div className="max-h-[85vh] overflow-y-auto">
              <RulesetForm
                initialData={ruleset}
                onSubmit={handleUpdateRuleset}
                onCancel={() => setShowEditDialog(false)}
                isLoading={updateRulesetMutation.isPending}
              />
            </div>
          </Dialog.Content>
        </Dialog.Root>

        {/* Share Dialog */}
        <Dialog.Root open={showShareDialog} onOpenChange={setShowShareDialog}>
          <Dialog.Content className="max-w-md">
            <Dialog.Title>Share Ruleset</Dialog.Title>
            <Box className="space-y-4">
              <Text size="2" color="gray">
                Share this public ruleset with others using the link below:
              </Text>

              {ruleset.shareToken && (
                <Box className="space-y-3">
                  <Box className="rounded border bg-gray-50 p-3 dark:bg-gray-800">
                    <Text size="1" className="break-all font-mono">
                      {`${window.location.origin}/rulesets/${ruleset.id}?token=${ruleset.shareToken}`}
                    </Text>
                  </Box>

                  <Flex gap="2">
                    <Button onClick={handleCopyShareLink} className="flex-1">
                      <Copy className="mr-2 h-4 w-4" />
                      Copy Link
                    </Button>
                    <Button variant="soft" onClick={() => setShowShareDialog(false)}>
                      Close
                    </Button>
                  </Flex>
                </Box>
              )}
            </Box>
          </Dialog.Content>
        </Dialog.Root>
      </Flex>
    </Container>
  );
}

export default function RulesetPage() {
  const params = useParams();
  const searchParams = useSearchParams();

  if (!params) {
    return null;
  }
  const rulesetId = params.id as string;
  const shareToken = searchParams?.get('token') ?? null;
  return <RulesetPageContent rulesetId={rulesetId} shareToken={shareToken} />;
}
