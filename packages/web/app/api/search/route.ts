// Temporarily disabled fumadocs search to fix build errors
// import { createFromSource } from 'fumadocs-core/search/server';
// import { source } from '@/lib/source';

// Mock search implementation until fumadocs is properly configured
export async function GET() {
  return new Response(JSON.stringify({ results: [] }), {
    status: 200,
    headers: {
      'Content-Type': 'application/json',
    },
  });
}
