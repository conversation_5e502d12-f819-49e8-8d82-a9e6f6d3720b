import { headers } from 'next/headers';
import { type NextRequest, NextResponse } from 'next/server';
import { auth } from '@/lib/auth';
import { prisma } from '@/lib/db';

export const dynamic = 'force-dynamic';

// Basic markdown parsing helpers (frontmatter + content heuristics)
function parseFrontmatter(input: string): { meta: Record<string, unknown>; body: string } {
  const fm = /^\s*---\s*\n([\s\S]*?)\n---\s*\n([\s\S]*)$/;
  const m = input.match(fm);
  if (!m) return { meta: {}, body: input.trim() };
  const [, fmStr, body] = m;
  const meta: Record<string, unknown> = {};
  for (const line of fmStr.split(/\r?\n/)) {
    const trimmed = line.trim();
    if (!trimmed || trimmed.startsWith('#')) continue;
    const idx = trimmed.indexOf(':');
    if (idx === -1) continue;
    const key = trimmed.slice(0, idx).trim();
    let value = trimmed.slice(idx + 1).trim();
    if (
      (value.startsWith('"') && value.endsWith('"')) ||
      (value.startsWith("'") && value.endsWith("'"))
    ) {
      value = value.slice(1, -1);
    }
    if (value.startsWith('[') && value.endsWith(']')) {
      const arr = value.slice(1, -1);
      meta[key] = arr
        ? arr
            .split(',')
            .map((i) => i.trim().replace(/^(["'])(.*)\1$/, '$2'))
            .filter(Boolean)
        : [];
    } else {
      meta[key] = value;
    }
  }
  return { meta, body: body.trim() };
}

function extractTitleAndDescription(body: string, fallbackTitle: string) {
  let title = fallbackTitle;
  const h1 = body.match(/^#\s+(.+)/m);
  if (h1) title = h1[1].trim();
  const desc = body.match(/^#\s+.+\n\n>\s*(.+)/m)?.[1]?.trim();
  return { title, description: desc || '' };
}

function extractGlob(body: string, meta: Record<string, unknown>): string | null {
  // Prefer frontmatter `glob`
  if (typeof meta.glob === 'string' && meta.glob.trim()) return meta.glob.trim();

  // Look in a "Metadata" section
  const metadataSection = body.match(/##\s+Metadata\s*\n([\s\S]*?)(?=\n##|\n#|$)/);
  if (metadataSection) {
    const s = metadataSection[1];
    const m1 = s.match(/\*\*Glob:\*\*\s*([^\n]+)/);
    if (m1) return m1[1].trim();
    const m2 = s.match(/-\s*glob:\s*([^\n]+)/i);
    if (m2) return m2[1].trim();
  }

  // Look inside code fences for a key named glob
  const codeBlocks = body.match(/```[a-zA-Z]*\n[\s\S]*?```/g) || [];
  for (const block of codeBlocks) {
    const globLine = block.match(/glob\s*[:=]\s*['"]?([^'"\n]+)['"]?/i);
    if (globLine) return globLine[1].trim();
  }

  // Heuristic: first line with typical glob wildcards
  const lines = body.split(/\r?\n/);
  for (const line of lines) {
    if (/[*?]\./.test(line) || /\*\*/.test(line)) {
      return line.trim();
    }
  }
  return null;
}

function sanitizeText(input: string, maxLen: number): string {
  const s = (input || '').toString().trim();
  return s.length > maxLen ? s.slice(0, maxLen) : s;
}

function deriveTags(meta: Record<string, unknown>): string[] {
  if (Array.isArray(meta.tags)) return meta.tags.map((t) => String(t));
  return [];
}

async function ensureTags(ruleId: string, tags: string[]) {
  for (const tagName of tags) {
    const name = tagName.trim();
    if (!name) continue;
    let tag = await prisma.tag.findUnique({ where: { name } });
    if (!tag) tag = await prisma.tag.create({ data: { name } });
    await prisma.ruleTag.create({ data: { ruleId, tagId: tag.id } });
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await auth.api.getSession({
      headers: await headers(),
    });
    if (!session?.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Enforce content type
    const contentType = request.headers.get('content-type') || '';
    if (!contentType.includes('multipart/form-data')) {
      return NextResponse.json({ error: 'Invalid content type' }, { status: 400 });
    }

    const form = await request.formData();
    const preview = String(form.get('preview') || 'false') === 'true';

    const files: File[] = [];
    for (const [key, value] of form.entries()) {
      if ((key === 'file' || key === 'files') && value instanceof File) {
        files.push(value);
      }
      if (value instanceof File && key.startsWith('file[')) {
        files.push(value);
      }
    }

    if (files.length === 0) {
      return NextResponse.json({ error: 'No files provided' }, { status: 400 });
    }

    // Constraints
    const MAX_SIZE = 1_000_000; // 1MB per file
    const ALLOWED_EXT = ['.md', '.mdx', '.markdown'];

    const results: Array<
      | { filename: string; status: 'ok'; rule?: unknown; parsed?: unknown }
      | { filename: string; status: 'error'; error: string }
    > = [];

    for (const f of files) {
      const filename = (f as any).name || 'upload.md';
      const lower = filename.toLowerCase();
      if (!ALLOWED_EXT.some((ext) => lower.endsWith(ext))) {
        results.push({
          filename,
          status: 'error',
          error: 'Only markdown files are allowed (.md, .mdx, .markdown)',
        });
        continue;
      }
      if (f.size > MAX_SIZE) {
        results.push({ filename, status: 'error', error: 'File too large. Max 1MB' });
        continue;
      }

      const text = await f.text();
      if (!text || /\u0000/.test(text)) {
        results.push({ filename, status: 'error', error: 'Invalid file content' });
        continue;
      }

      const { meta, body } = parseFrontmatter(text);
      const { title, description } = extractTitleAndDescription(
        body,
        filename.replace(/\.(md|mdx|markdown)$/i, '').replace(/[-_]/g, ' ')
      );
      const glob = extractGlob(body, meta);
      const tags = deriveTags(meta);

      // Build rule payload
      const payload = {
        title: sanitizeText(String(meta.title || title), 100),
        description: sanitizeText(String(meta.description || description || ''), 500) || null,
        content: body,
        visibility: String(meta.visibility || 'PRIVATE').toUpperCase() as 'PRIVATE' | 'PUBLIC',
        applyType: String(meta.applyType || 'manual').toLowerCase() as 'auto' | 'manual' | 'always',
        glob: glob || null,
        tags,
      };

      // Validate minimal requirements
      if (!payload.title) {
        results.push({
          filename,
          status: 'error',
          error: 'Missing title (H1 or frontmatter title)',
        });
        continue;
      }
      if (!['PRIVATE', 'PUBLIC'].includes(payload.visibility)) payload.visibility = 'PRIVATE';
      if (!['auto', 'manual', 'always'].includes(payload.applyType)) payload.applyType = 'manual';

      if (preview) {
        results.push({ filename, status: 'ok', parsed: payload });
        continue;
      }

      // Persist
      const created = await prisma.rule.create({
        data: {
          title: payload.title,
          description: payload.description,
          content: payload.content,
          visibility: payload.visibility,
          applyType: payload.applyType,
          glob: payload.glob,
          userId: session.user.id,
          shareToken: payload.visibility === 'PUBLIC' ? crypto.randomUUID() : null,
        },
        include: {
          tags: { include: { tag: true } },
        },
      });

      // Ensure tags
      if (payload.tags?.length) {
        await ensureTags(created.id, payload.tags);
      }

      const final = await prisma.rule.findUnique({
        where: { id: created.id },
        include: {
          tags: { include: { tag: true } },
          user: { select: { id: true, name: true, email: true } },
        },
      });

      results.push({ filename, status: 'ok', rule: final });
    }

    const hasErrors = results.some((r) => r.status === 'error');
    return NextResponse.json({ results }, { status: hasErrors ? 207 : 200 });
  } catch (error) {
    console.error('Error uploading markdown rules:', error);
    return NextResponse.json({ error: 'Failed to upload rules' }, { status: 500 });
  }
}
