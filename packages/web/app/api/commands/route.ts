import { NextResponse } from 'next/server';
import { getCommandRule } from '@/lib/docs-command-normalizer';

export async function GET() {
  try {
    const isBuildTime =
      process.env.NODE_ENV === 'production' &&
      !process.env.VERCEL &&
      !process.env.RAILWAY_ENVIRONMENT;

    if (isBuildTime) {
      return NextResponse.json(
        {
          error: 'Service temporarily unavailable during build',
        },
        { status: 503 }
      );
    }

    const rule = getCommandRule();

    if (!rule) {
      return NextResponse.json(
        {
          success: false,
          error: 'Command rule not found',
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: rule,
      message: 'Found command rule',
    });
  } catch (error) {
    console.error('Error fetching command rule:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Failed to fetch command rule',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
