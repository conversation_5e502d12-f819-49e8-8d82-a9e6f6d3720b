// Temporarily disabled fumadocs UI to fix build errors
// import { DocsBody, DocsDescription, DocsPage, DocsTitle } from 'fumadocs-ui/page';
import type { Metadata } from 'next';
import { notFound } from 'next/navigation';
// import { source } from '@/lib/source';
// import { getMDXComponents } from '@/mdx-components';

export default async function Page(props: { params: Promise<{ slug?: string[] }> }) {
  const params = await props.params;
  // const page = source.getPage(params.slug);
  // if (!page) {
  //   notFound();
  // }
  // const MDX = page.data.body;
  
  // Mock page data until fumadocs is properly configured
  const page = {
    data: {
      title: 'Documentation',
      description: 'Documentation page',
      body: null,
      toc: [],
      full: false,
    }
  };
  
  return (
    // <DocsPage toc={page.data.toc} full={page.data.full}>
      <div className="max-w-4xl mx-auto">
        {/* <DocsTitle>{page.data.title}</DocsTitle> */}
        <h1 className="text-3xl font-bold mb-4">{page.data.title}</h1>
        {/* <DocsDescription>{page.data.description}</DocsDescription> */}
        <p className="text-gray-600 mb-8">{page.data.description}</p>
        {/* <DocsBody> */}
        <div className="prose max-w-none">
          {/* <MDXContent code={page.data.body} components={getMDXComponents()} /> */}
          {/* <MDX components={getMDXComponents()} /> */}
          <p>Documentation content will be available once fumadocs is properly configured.</p>
        </div>
        {/* </DocsBody> */}
      </div>
    // </DocsPage>
  );
}

export async function generateStaticParams() {
  // return source.generateParams();
  return []; // Return empty array until fumadocs is configured
}

export async function generateMetadata(props: { params: Promise<{ slug?: string[] }> }) {
  const params = await props.params;
  // const page = source.getPage(params.slug);
  // if (!page) notFound();
  
  // Mock metadata until fumadocs is configured
  return {
    title: 'Documentation',
    description: 'Documentation page',
  } satisfies Metadata;
}
