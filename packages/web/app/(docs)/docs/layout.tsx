// Temporarily disabled fumadocs UI to fix build errors
// import { DocsLayout } from 'fumadocs-ui/layouts/docs';
import type { ReactNode } from 'react';
// import { source } from '@/lib/source';
import '../docs.css';
// import { RootProvider } from 'fumadocs-ui/provider';

export default function Layout({ children }: { children: ReactNode }) {
  return (
    // <RootProvider>
    //   <DocsLayout tree={source.pageTree}>
        <div className="p-[24px]">
          <div className="min-h-screen">
            <nav className="border-b p-4">
              <h1 className="text-xl font-bold">Documentation</h1>
            </nav>
            <main className="p-4">
              {children}
            </main>
          </div>
        </div>
      // </DocsLayout>
    // </RootProvider>
  );
}
