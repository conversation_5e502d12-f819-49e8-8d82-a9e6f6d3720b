import { createMDX } from 'fumadocs-mdx/next';

/** @type {import('next').NextConfig} */
const config = {
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  transpilePackages: ['@onlyrules/shared', '@onlyrules/docs', 'better-auth'],
  output: 'standalone',
  // Force App Router and disable Pages Router
  trailingSlash: false,

  async rewrites() {
    return [
      { source: '/sitemap.xml', destination: '/api/sitemap.xml' },
      { source: '/robots.txt', destination: '/api/robots.txt' },
    ];
  },

  generateBuildId: async () => `build-${Date.now()}`,

  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          { key: 'Cache-Control', value: 'no-store, must-revalidate' },
        ],
      },
    ];
  },
};

const withMDX = createMDX({
  // customise the config file path
  configPath: 'fumadocs.config.ts',
});

export default withMDX(config);

