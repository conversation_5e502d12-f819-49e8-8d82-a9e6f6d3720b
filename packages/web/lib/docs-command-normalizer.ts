import { readFileSync } from 'node:fs';
import { join } from 'node:path';
import type { Rule } from './store';

interface MDCFrontmatter {
  title?: string;
  description?: string;
  author?: string;
  tags?: string[];
  [key: string]: unknown;
}

function parseFrontmatter(content: string): {
  metadata: MDCFrontmatter;
  content: string;
} {
  const frontmatterRegex = /^\s*---\s*\r?\n([\s\S]*?)\r?\n---\s*\r?\n([\s\S]*)$/;
  const match = content.match(frontmatterRegex);

  if (!match) {
    return { metadata: {}, content: content.trim() };
  }

  const [, frontmatterStr, markdownContent] = match;
  const metadata: MDCFrontmatter = {};

  const lines = frontmatterStr.split(/\r?\n/);
  for (const line of lines) {
    const trimmedLine = line.trim();
    if (!trimmedLine || trimmedLine.startsWith('#')) continue;

    const colonIndex = trimmedLine.indexOf(':');
    if (colonIndex === -1) continue;

    const key = trimmedLine.slice(0, colonIndex).trim();
    let value = trimmedLine.slice(colonIndex + 1).trim();

    if (!value) continue;

    if (value.startsWith('[') && value.endsWith(']')) {
      const arrayContent = value.slice(1, -1);
      const items = arrayContent
        .split(',')
        .map((item) => item.trim().replace(/^["']|["']$/g, ''))
        .filter(Boolean);
      metadata[key] = items;
      continue;
    }

    if (
      (value.startsWith('"') && value.endsWith('"')) ||
      (value.startsWith("'") && value.endsWith("'"))
    ) {
      value = value.slice(1, -1);
    }

    metadata[key] = value;
  }

  return { metadata, content: markdownContent.trim() };
}

function generateRuleId(filename: string): string {
  const baseName = filename.replace(/\.md$/, '');
  return `command_${baseName}`;
}

export function normalizeCommandToRule(filePath: string, content: string): Rule {
  const { metadata, content: ruleContent } = parseFrontmatter(content);
  const filename = filePath.split('/').pop() || 'unknown.md';

  const now = new Date().toISOString();
  const ruleId = generateRuleId(filename);

  return {
    id: ruleId,
    title: metadata.title || filename.replace(/\.md$/, '').replace(/[-_]/g, ' '),
    description: metadata.description || null,
    content: ruleContent,
    visibility: 'PUBLIC' as const,
    applyType: 'manual' as const,
    glob: undefined,
    shareToken: null,
    createdAt: now,
    updatedAt: now,
    userId: 'system',
    tags: (metadata.tags || []).map((tagName: string) => ({
      tag: {
        id: `tag_${tagName.toLowerCase().replace(/\s+/g, '_')}`,
        name: tagName,
        color: '#3B82F6',
      },
    })),
    user: {
      id: 'system',
      name: metadata.author || 'System',
      email: '<EMAIL>',
      image: null,
      avatar: null,
    },
  };
}

export function getCommandRule(): Rule | null {
  const cwd = process.cwd();
  const isInWebPackage = cwd.includes('packages/web');
  const commandPath = isInWebPackage
    ? join(cwd, '../../packages/docs/commands/commit.md')
    : join(cwd, 'packages/docs/commands/commit.md');

  try {
    const content = readFileSync(commandPath, 'utf-8');
    const rule = normalizeCommandToRule('commit.md', content);
    return rule;
  } catch (error) {
    console.error('Error reading command rule:', error);
    return null;
  }
}
