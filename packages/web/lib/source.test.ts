import { describe, expect, it, vi } from 'vitest';

// Mock Fumadocs runtime dependencies to avoid requiring MDX/Vite transforms in Vitest
vi.mock('@/.source', () => ({
  docs: { toFumadocsSource: () => ({}) },
}));
vi.mock('fumadocs-core/source', () => ({
  loader: () => ({
    getPage: (slug: string[]) =>
      slug.join('/') === 'index' ? { data: { body: () => null } } : undefined,
  }),
}));

import { source } from './source';

// Minimal integration test to assert that source.getPage works for a known slug

describe('fumadocs source', () => {
  it('returns page data for index slug', () => {
    const page = source.getPage(['index']);
    expect(page).toBeTruthy();
    expect(page?.data).toBeTruthy();
    // MDX body should be a renderable component
    expect(typeof page?.data?.body).toBe('function');
  });
});
