import { Box, Container, Flex, Text } from '@radix-ui/themes';
import { Code, Github } from 'lucide-react';
import Link from 'next/link';
import { FooterLink } from './footer-link';

export function Footer() {
  const currentYear = new Date().getFullYear();

  return (
    <Box asChild>
      <footer
        style={{
          marginTop: 'auto',
          borderTopWidth: '1px',
          borderTopStyle: 'solid',
          borderTopColor: 'var(--gray-6)',
          backgroundColor: 'var(--color-panel-solid)',
        }}
      >
        <Container size="4" px={{ initial: '5', sm: '6' }} py={{ initial: '4' }}>
          <Flex
            direction={{ initial: 'column', sm: 'row' }}
            align={{ initial: 'start', sm: 'center' }}
            justify="between"
            gap="3"
          >
            {/* Brand */}
            <Link href="/">
              <Flex align="center" gap="2">
                <Code size={18} className="text-[var(--accent-9)]" />
                <Text size="3" weight="bold" color="gray" highContrast>
                  OnlyRules
                </Text>
              </Flex>
            </Link>

            {/* Inline links */}
            <Flex
              align="center"
              wrap="wrap"
              gap={{ initial: '3', sm: '4' }}
              style={{ lineHeight: 1 }}
            >
              <FooterLink href="/docs">Documentation</FooterLink>
              <FooterLink href="/rulesets">Ruleset Library</FooterLink>
              <FooterLink href="/ides">IDE Integrations</FooterLink>
              <FooterLink href="/contact">Contact</FooterLink>
              <FooterLink href="/privacy">Privacy</FooterLink>
              <FooterLink href="/terms">Terms</FooterLink>
              <FooterLink href="https://github.com/ranglang/onlyrules" external>
                <Flex align="center" gap="2">
                  <Github size={16} />
                  GitHub
                </Flex>
              </FooterLink>
            </Flex>

            {/* Copyright */}
            <Text size="2" color="gray">
              © {currentYear} OnlyRules
            </Text>
          </Flex>

          {/* Structured Data for SEO */}
          <script
            type="application/ld+json"
            dangerouslySetInnerHTML={{
              __html: JSON.stringify({
                '@context': 'https://schema.org',
                '@type': 'Organization',
                name: 'OnlyRules',
                description: 'AI Prompt Management Platform for Developers',
                url: process.env.NEXT_PUBLIC_APP_URL || 'https://onlyrules.codes',
                logo: `${process.env.NEXT_PUBLIC_APP_URL || 'https://onlyrules.codes'}/logo.png`,
                sameAs: ['https://github.com/ranglang/onlyrules'],
                contactPoint: {
                  '@type': 'ContactPoint',
                  email: '<EMAIL>',
                  contactType: 'Customer Support',
                },
              }),
            }}
          />
        </Container>
      </footer>
    </Box>
  );
}
