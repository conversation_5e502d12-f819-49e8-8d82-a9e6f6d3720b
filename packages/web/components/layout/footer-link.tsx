'use client';

import { Text } from '@radix-ui/themes';
import { ExternalLink } from 'lucide-react';
import Link from 'next/link';

interface FooterLinkProps {
  href: string;
  children: React.ReactNode;
  external?: boolean;
  className?: string;
  variant?: 'default' | 'surface';
}

export function FooterLink({
  href,
  children,
  external = false,
  className,
  variant = 'default',
}: FooterLinkProps) {
  const linkProps = external
    ? {
        target: '_blank' as const,
        rel: 'noopener noreferrer' as const,
      }
    : {};

  const baseStyles =
    variant === 'surface'
      ? {
          display: 'inline-block',
          borderRadius: 'var(--radius-3)',
          transition: 'all 0.15s ease',
          backgroundColor: 'transparent',
        }
      : {
          display: 'inline-block',
          transition: 'color 0.15s ease',
        };

  const hoverStyles =
    variant === 'surface'
      ? ':hover { background-color: var(--gray-3); }'
      : ':hover { color: var(--accent-9); }';

  return (
    <>
      <style jsx>{`
        .footer-link${variant === 'surface' ? '-surface' : ''} ${hoverStyles}
      `}</style>
      <Link
        href={href}
        {...linkProps}
        className={`footer-link${variant === 'surface' ? '-surface' : ''} ${className || ''}`}
        style={baseStyles}
      >
        {variant === 'surface' ? (
          children
        ) : (
          <Text
            size="2"
            color="gray"
            style={{
              display: 'flex',
              alignItems: 'center',
              gap: '6px',
              transition: 'color 0.15s ease',
            }}
          >
            {children}
            {external && <ExternalLink size={12} style={{ opacity: 0.7 }} />}
          </Text>
        )}
      </Link>
    </>
  );
}
