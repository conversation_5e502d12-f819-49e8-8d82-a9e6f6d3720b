'use client';

import { <PERSON>, But<PERSON>, Card, Checkbox, Flex, Progress, Text } from '@radix-ui/themes';
import { AlertTriangle, CheckCircle2, CloudUpload, FileText, XCircle } from 'lucide-react';
import { useCallback, useMemo, useRef, useState } from 'react';
import { toast } from 'sonner';

interface UploadResultOk {
  filename: string;
  status: 'ok';
  rule?: any;
  parsed?: any;
}
interface UploadResultErr {
  filename: string;
  status: 'error';
  error: string;
}

type UploadResult = UploadResultOk | UploadResultErr;

interface RuleUploadDropzoneProps {
  onCompleted?: (createdCount: number) => void;
}

const ALLOWED_EXT = ['.md', '.mdx', '.markdown'];
const MAX_SIZE = 1_000_000; // 1MB

export function RuleUploadDropzone({ onCompleted }: RuleUploadDropzoneProps) {
  const [dragOver, setDragOver] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [results, setResults] = useState<UploadResult[] | null>(null);
  const [previewOnly, setPreviewOnly] = useState(true);
  const inputRef = useRef<HTMLInputElement | null>(null);

  const isMarkdownFile = useCallback((file: File): boolean => {
    const name = file.name.toLowerCase();
    if (ALLOWED_EXT.some((ext) => name.endsWith(ext))) return true;
    // Some browsers set type to empty for .md/.mdx; accept based on extension
    return file.type === 'text/markdown' || file.type === 'text/x-markdown';
  }, []);

  const _invalidFiles = useMemo(() => {
    return selectedFiles.filter((f) => !isMarkdownFile(f) || f.size > MAX_SIZE);
  }, [selectedFiles, isMarkdownFile]);

  const onDropFiles = useCallback((files: FileList | File[]) => {
    const list = Array.from(files);
    setSelectedFiles(list);
    setResults(null);
  }, []);

  const handleBrowse = () => inputRef.current?.click();

  const handleInputChange: React.ChangeEventHandler<HTMLInputElement> = (e) => {
    const files = e.target.files;
    if (files?.length) onDropFiles(files);
  };

  const handleUpload = async (doPreview: boolean) => {
    if (selectedFiles.length === 0) {
      toast.error('Please select at least one markdown file');
      return;
    }

    // Client-side validation
    for (const f of selectedFiles) {
      if (!isMarkdownFile(f)) {
        toast.error(`Invalid file type: ${f.name}`);
        return;
      }
      if (f.size > MAX_SIZE) {
        toast.error(`File too large (max 1MB): ${f.name}`);
        return;
      }
    }

    setUploading(true);
    setProgress(0);
    setResults(null);

    try {
      const form = new FormData();
      // Append files as repeated "file" fields
      for (const f of selectedFiles) form.append('file', f);
      form.append('preview', doPreview ? 'true' : 'false');

      // We cannot track true network bytes easily; fake step progress
      setProgress(30);
      const res = await fetch('/api/rules/upload', { method: 'POST', body: form });
      setProgress(80);
      const json = await res.json();

      const list: UploadResult[] = json?.results || [];
      setResults(list);
      setProgress(100);

      const errors = list.filter((r) => r.status === 'error') as UploadResultErr[];
      const oks = list.filter((r) => r.status === 'ok') as UploadResultOk[];

      if (doPreview) {
        if (errors.length) {
          toast.warning(`Preview parsed with ${errors.length} error(s)`);
        } else {
          toast.success('Preview parsed successfully');
        }
      } else {
        const created = oks.filter((r) => !!r.rule).length;
        if (created > 0) toast.success(`Created ${created} rule(s)`);
        if (errors.length) toast.warning(`${errors.length} file(s) failed`);
        onCompleted?.(created);
      }
    } catch (e: any) {
      console.error(e);
      toast.error('Upload failed');
    } finally {
      setUploading(false);
      setTimeout(() => setProgress(0), 600);
    }
  };

  const handleDrop: React.DragEventHandler<HTMLDivElement> = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragOver(false);
    if (e.dataTransfer?.files?.length) onDropFiles(e.dataTransfer.files);
  };

  const handleDragOver: React.DragEventHandler<HTMLDivElement> = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragOver(true);
  };

  const handleDragLeave: React.DragEventHandler<HTMLDivElement> = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragOver(false);
  };

  return (
    <Box>
      <Card
        className={`border-2 border-dashed ${dragOver ? 'border-blue-500 bg-blue-50 dark:bg-blue-950/30' : 'border-gray-300 dark:border-gray-700'}`}
      >
        <div
          onDrop={handleDrop}
          onDragOver={handleDragOver}
          onDragLeave={handleDragLeave}
          className="flex flex-col items-center justify-center gap-2 p-8 text-center"
        >
          <CloudUpload className={`h-10 w-10 ${dragOver ? 'text-blue-600' : 'text-gray-500'}`} />
          <Text size="3" weight="medium">
            Drag & drop markdown files
          </Text>
          <Text size="2" color="gray">
            .md, .mdx up to 1MB each
          </Text>
          <Flex mt="3" gap="3">
            <Button onClick={handleBrowse} disabled={uploading}>
              Browse files
            </Button>
            <input
              ref={inputRef}
              type="file"
              accept=".md,.mdx,.markdown,text/markdown"
              multiple
              onChange={handleInputChange}
              className="hidden"
            />
          </Flex>
        </div>
      </Card>

      {/* Selected files list */}
      {selectedFiles.length > 0 && (
        <Box mt="4" className="space-y-2">
          {selectedFiles.map((f) => {
            const isInvalid = !isMarkdownFile(f) || f.size > MAX_SIZE;
            return (
              <Flex
                key={f.name + f.size}
                align="center"
                justify="between"
                className="rounded-md border px-3 py-2"
              >
                <Flex align="center" gap="2">
                  <FileText className="h-4 w-4" />
                  <Text>{f.name}</Text>
                  {isInvalid && (
                    <Flex align="center" gap="1" className="text-red-600">
                      <AlertTriangle className="h-4 w-4" />
                      <Text color="red" size="1">
                        Invalid
                      </Text>
                    </Flex>
                  )}
                </Flex>
                <Text size="1" color="gray">
                  {Math.ceil(f.size / 1024)} KB
                </Text>
              </Flex>
            );
          })}
        </Box>
      )}

      {/* Controls */}
      <Flex align="center" justify="between" mt="4">
        <Flex align="center" gap="2">
          <Checkbox
            checked={previewOnly}
            onCheckedChange={(v) => setPreviewOnly(!!v)}
            id="preview-only"
          />
          <label htmlFor="preview-only" className="text-sm">
            Preview only (dont create yet)
          </label>
        </Flex>
        <Flex gap="2">
          <Button
            variant="soft"
            onClick={() => handleUpload(true)}
            disabled={uploading || selectedFiles.length === 0}
          >
            Preview
          </Button>
          <Button
            onClick={() => handleUpload(!!previewOnly)}
            disabled={uploading || selectedFiles.length === 0}
          >
            {previewOnly ? 'Preview & Review' : 'Upload & Create'}
          </Button>
        </Flex>
      </Flex>

      {/* Progress */}
      {uploading && (
        <Box mt="3">
          <Progress value={progress} max={100} />
        </Box>
      )}

      {/* Results */}
      {results && (
        <Box mt="4" className="space-y-2">
          {results.map((r) => (
            <Flex
              key={r.filename + r.status}
              align="center"
              justify="between"
              className="rounded-md border px-3 py-2"
            >
              <Flex align="center" gap="2">
                {r.status === 'ok' ? (
                  <CheckCircle2 className="h-4 w-4 text-green-600" />
                ) : (
                  <XCircle className="h-4 w-4 text-red-600" />
                )}
                <Text weight="medium">{r.filename}</Text>
                {r.status === 'ok' && (r as UploadResultOk).parsed && (
                  <Text size="1" color="gray">
                    parsed
                  </Text>
                )}
                {r.status === 'ok' && (r as UploadResultOk).rule && (
                  <Text size="1" color="gray">
                    created
                  </Text>
                )}
                {r.status === 'error' && <Text color="red">{(r as UploadResultErr).error}</Text>}
              </Flex>
            </Flex>
          ))}

          {/* If preview, show a create button */}
          {previewOnly && results.some((r) => r.status === 'ok') && (
            <Flex justify="end" mt="2">
              <Button onClick={() => handleUpload(false)}>
                Create {results.filter((r) => r.status === 'ok').length} rule(s)
              </Button>
            </Flex>
          )}
        </Box>
      )}
    </Box>
  );
}

export default RuleUploadDropzone;
