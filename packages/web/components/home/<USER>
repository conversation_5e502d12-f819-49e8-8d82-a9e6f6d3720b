'use client';

import { Box, Heading } from '@radix-ui/themes';
import { RuleCard } from '@/components/rule-card';
import type { Rule } from '@/lib/store';

interface CommandRuleSectionProps {
  rule: Rule;
}

export function CommandRuleSection({ rule }: CommandRuleSectionProps) {
  if (!rule) return null;

  return (
    <Box py="8">
      <Heading size="6" mb="4" align="center">
        Featured Command
      </Heading>
      <RuleCard rule={rule} />
    </Box>
  );
}
