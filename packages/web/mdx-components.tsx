// import type { MDXComponents } from 'mdx/types';

// Provide MDX components mapping if you have custom components.
// For now we return an empty map so MDX renders with default elements.
// export function getMDXComponents(): MDXComponents {
//   return {};
// }

import defaultMdxComponents from 'fumadocs-ui/mdx';
import type { MDXComponents } from 'mdx/types';

export function getMDXComponents(components?: MDXComponents): MDXComponents {
  return {
    ...defaultMdxComponents,
    ...components,
  };
}
