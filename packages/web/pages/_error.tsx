type ErrorProps = {
  statusCode?: number;
};

function ErrorPage({ statusCode }: ErrorProps) {
  return (
    <div
      style={{
        minHeight: '100vh',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      }}
    >
      <div style={{ textAlign: 'center' }}>
        <h1>{statusCode || 500}</h1>
        <p>{statusCode ? `An error ${statusCode} occurred` : 'An unexpected error occurred'}</p>
        <a href="/">Go Home</a>
      </div>
    </div>
  );
}

ErrorPage.getInitialProps = ({
  res,
  err,
}: {
  res?: { statusCode?: number };
  err?: { statusCode?: number };
}) => {
  const statusCode = res?.statusCode ?? err?.statusCode ?? 404;
  return { statusCode } as ErrorProps;
};

export default ErrorPage;
