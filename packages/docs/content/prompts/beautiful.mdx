---
title: test ui prompt
description: test
toc: false
full: true
---

design a substack alternative for user can use group customized agent , they can get their artifact. NEO-MORPHIC GLASSMORPHISM REFERENCE — FULL VISUAL TEARDOWN (Study every detail below; this is your single-source brief) COLOR DNA • Primary canvas = void black (#0A0A0B). • Secondary blocks = cyan glow (#00D9FF) and violet pulse (#8B5CF6). • Accent highlight = emerald flash (#10B981). • Glass layers = white alpha 5% (#FFFFFF0D) and frost white 2% (#FFFFFF05). • Rule: never flat colors; always gradient meshes with 10% opacity noise overlay for depth perception.

TYPOGRAPHY • Weight = 300-600 variable, optical sizing on, high x-height for screen clarity. • Case mixing: sentence-case for headlines, all-lowercase for meta text (edgy tech vibe). • Scale: think cinematic — "Glassmorphism" uses ~7-8 vw, sub-words roughly 40% of that size. • Wide letter-spacing (0.02em to 0.08em), line-height 1.2 for headings, 1.7 for body. • Recommended pairing: Clash Display (display) + <PERSON><PERSON> (body). SHAPE & GEOMETRY Hero glass — a floating panel, backdrop-blur 40px, corner-radius ≈24px; 1px glow border with animated gradient. Ambient orbs — blurred circles with radial gradient, opacity 20%, positioned absolute behind content. Data cards — glass rectangles with inset shadow, slight Y-axis float on hover (+translateY -4px). Device frame — full interface shown in perspective transform3d(5deg), massive 32px blur shadow underneath. DEPTH & MOTION CUES • Shadows are SOFT; blur-radius 40px+, opacity ~40%, colored tints (cyan/violet). • Hover states: 4px upward float, shadow intensifies, glass shimmer sweeps across. • Aurora highlights: gradient bar morphing between cyan-violet-emerald behind key elements (animate hue-rotate). PATTERN & RHYTHM • Layout uses z-space stacking: background orbs → glass panels → content → floating CTAs (maintain depth hierarchy). • Every glass surface has dual-layer construction: base blur + noise texture at 2% opacity. • Whitespace isn't empty; it contains subtle animated grain and floating particles at 5% opacity. ICONOGRAPHY & SMALL GRAPHICS • All icons use thin 1.5px strokes with gradient fills (never solid). • Utilize phosphor or tabler icons for consistency, always with glow effect on interaction. • No hard edges except for code blocks which get sharp 1px borders for contrast. MICRO-INTERACTIONS (required) • Mouse-parallax on glass panels, 10px movement range, ease-out-expo timing. • Scroll-triggered opacity fade-in starting at 80% viewport visibility, stagger children 100ms. • prefers-reduced-motion → keep glass effects, disable parallax and auto-animations.

COMPONENT BLUEPRINTS — Navigation: fixed glass bar, backdrop-blur 20px, logo has aurora glow, links have 2px underline reveal on hover. — CTA Buttons: glass pill, gradient border, shimmer sweep on hover; active → scale(0.98) with inset shadow. — Stat Badge: frosted circle, pulsing glow animation, number counter animation on viewport entry. — Image Frames: 20px radius, 1px subtle border, wrapped in glass panel with 40px padding for breathing room. CODE & PERFORMANCE NOTES • Stack: Next.js + Tailwind + Framer Motion. Add backdrop-filter utilities to config. • Use CSS custom properties for glass layers to enable real-time theme switching. • SVG filters for noise texture, inline critical filters, lazy-load decorative elements. • Composite layers for all glass elements via will-change: transform, backdrop-filter. VOICE & COPY STYLE Minimal, sophisticated, future-forward. Single words as statements: "Transform.", "Evolve.", "Transcend." One concept per viewport: massive headline, minimal subtext, ghost CTA. MINIMUM DELIVERABLES FOR REDESIGN Glass utilities Tailwind plugin. GlassPanel + GlowButton + NoiseTexture components. Ambient orb particle system. Parallax and shimmer animation presets. Contrast validation (WCAG AA on glass). Performance testing with blur effects at 60fps. Nail every point above and the site will perfectly capture the ethereal, dimensional neo-morphic aesthetic that makes users feel like they're interacting with tomorrow's interfaces.