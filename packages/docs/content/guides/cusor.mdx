---
title: Cursor Rules Guide
description: Understand modern Cursor rules (.mdc), including project and user rules, metadata, and best practices.
---

# Cursor Rules Guide

## Overview

Cursor Rules let you encode your team’s knowledge and preferences so the AI consistently follows your standards. Use them to align generated code with your architecture, coding style, libraries, and workflow.

## Why use Cursor Rules

- Consistent outputs across the codebase
- Faster onboarding for new contributors
- Less rework and fewer review cycles
- Safer automation for repetitive tasks

## Rule types

- Project Rules: Version-controlled, live in `.cursor/rules`, scoped to a single repository.
- User Rules: Global preferences configured in Cursor settings; apply to all projects.
- Note: The legacy `.cursorrules` file is still supported but deprecated. Prefer Project Rules.

## Setup (Project Rules)

1. Create a `.cursor/rules` directory in your project root.
2. Add one rule per file using the `.mdc` extension.
3. Each rule contains metadata (frontmatter) and rule content.

Example `.mdc` file:

```mdc
---
description: Optional description for Agent Requested rules
globs: ["optional/glob/pattern/**", "*.py"]
alwaysApply: false
---

# Rule content
- Prefer this library/pattern when building feature X
- Avoid anti-pattern Y
- Reference for context: @path/to/important/file.ts
```

## How rules are applied (metadata → behavior)

- Always: `alwaysApply: true` — always included in the model context for the project.
- Auto Attached: `globs: ["pattern"]` — included only when files matching the globs are in context.
- Agent Requested: `description: "..."` — the AI may fetch the rule when relevant.
- Manual: No special metadata — include explicitly in chat with `@ruleName`.

## Working with rules

- Create via Command Palette: New Cursor Rule
- Generate from chat: `/Generate Cursor Rules` converts a refined chat into a rule
- Keep files focused and small; split large topics into multiple rules
- Use descriptive names and organize by domain (e.g., `react.mdc`, `django.mdc`, `docker.mdc`)

## Best practices

- Be specific and actionable; avoid vague guidance
- Keep them concise; aim for clarity over completeness
- Show small examples and reference key files with `@path/to/file`
- Decompose complex standards into smaller rules
- Iterate based on AI performance and review feedback

## Examples (concise)

### Django best practices (`django.mdc`)

```mdc
---
description: Django models, views, templates, ORM usage
globs: ["*.py"]
---

# Django Guidelines
- Follow PEP 8 and Django style guides
- Use CBVs for common patterns; FBVs for custom logic
- Optimize queries with select_related/prefetch_related
- Validate with ModelForm and `clean()` methods
```

### Dockerfile best practices (`docker.mdc`)

```mdc
---
description: Efficient and secure Dockerfiles
globs: ["Dockerfile", "*.Dockerfile"]
---

# Dockerfile Guidelines
- Use minimal, pinned base images
- Prefer multi-stage builds; leverage cache
- Run as non-root; minimize packages
- Use COPY over ADD unless necessary
```

### Python Black formatting (`black.mdc`)

```mdc
---
description: Enforce Black formatting
globs: ["*.py"]
---

# Black Rules
- Use default 88 char line length
- Ensure CI checks with `black --check .`
```

### React best practices (`react.mdc`)

```mdc
---
description: Modern React guidelines
globs: ["*.js", "*.jsx", "*.ts", "*.tsx"]
---

# React Guidelines
- Prefer function components + Hooks
- Co-locate by feature; use clear prop types/interfaces
- Use `useEffect` with explicit dependencies; memoize when needed
```

## User Rules: global preferences

Set in Cursor Settings → Rules → User Rules (plain text). Examples:

- Always import React explicitly: `import React from 'react'`
- Respond concisely; avoid filler
- Prefer functional components over classes

## FAQs

- Is `.cursorrules` deprecated? Yes, prefer `.cursor/rules/*.mdc`.
- Can I combine metadata? Yes; e.g., include `globs` and a `description`.
- How big should a rule be? Keep it focused; split beyond ~500 lines.

## Next steps

- Start with a small set of high-impact rules (e.g., review guidelines, API patterns)
- Iterate after a sprint based on friction you notice
- Promote stable rules to “Always” when they prove universally helpful