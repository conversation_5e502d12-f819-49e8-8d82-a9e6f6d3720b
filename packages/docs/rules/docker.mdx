---
description: Guidelines for writing efficient and secure Dockerfiles.
globs: ["Dockerfile", "*.Dockerfile"]
alwaysApply: false
---

# Dockerfile Best Practices

## Base Image
- Use official, minimal base images (e.g., `python:3.11-slim`, `node:18-alpine`, `golang:1.20-alpine`).
- Specify exact versions or digests for reproducibility (`python:3.11.4-slim-bullseye`).
- Prefer Alpine images for size, but be aware of potential `musl libc` compatibility issues vs `glibc`.

## Minimize Layers & Build Context
- Chain related commands using `&&` to reduce layers (e.g., `apt-get update && apt-get install -y --no-install-recommends package && rm -rf /var/lib/apt/lists/*`).
- Use a `.dockerignore` file to exclude unnecessary files/directories (e.g., `.git`, `node_modules`, `.venv`, logs, local config) from the build context.

## Build Efficiency
- Order instructions from least to most frequently changing (e.g., install dependencies before copying application code).
- Leverage Docker build cache effectively.
- Use multi-stage builds to keep the final image small, separating build dependencies from runtime dependencies.

## Security
- Run containers as a non-root user. Create a dedicated user/group (`RUN groupadd -r appuser && useradd -r -g appuser appuser`) and switch to it (`USER appuser`).
- Avoid storing secrets directly in the Dockerfile. Use build arguments (`ARG`), environment variables (`ENV`), or secrets management tools at runtime.
- Scan images for vulnerabilities using tools like Trivy or Docker Scout.
- Minimize the attack surface: install only necessary packages (`--no-install-recommends`).

## Instructions
- Use `COPY` instead of `ADD` unless you specifically need `ADD`'s features (like auto-extraction or URL fetching).
- Prefer `ENTRYPOINT` for the main container command and use `CMD` for default arguments that can be overridden. Use the JSON array form (e.g., `ENTRYPOINT ["python", "app.py"]`).
- Use `WORKDIR` to set the working directory for subsequent instructions (`RUN`, `CMD`, `ENTRYPOINT`, `COPY`, `ADD`).
- Expose necessary ports using `EXPOSE`.

## Example Multi-Stage Build (Python)
```dockerfile
# Build Stage
FROM python:3.11-slim as builder
WORKDIR /app
COPY requirements.txt .
RUN pip install --no-cache-dir --user -r requirements.txt
COPY . .

# Final Stage
FROM python:3.11-alpine
WORKDIR /app
# Create non-root user
RUN addgroup -S appgroup && adduser -S appuser -G appgroup
# Copy installed packages and app code from builder stage
COPY --from=builder /root/.local /usr/local
COPY --from=builder /app .
# Ensure scripts are executable if needed
# RUN chmod +x ./entrypoint.sh
USER appuser
EXPOSE 8000
ENTRYPOINT ["python"]
CMD ["manage.py", "runserver", "0.0.0.0:8000"]
