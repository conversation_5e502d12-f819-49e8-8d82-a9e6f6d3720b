{"name": "@onlyrules/docs", "version": "0.1.0", "private": true, "main": "./lib/index.ts", "types": "./lib/index.ts", "exports": {".": "./lib/index.ts", "./source": "./lib/source.ts", "./utils": "./lib/utils.ts", "./css": "./dist/index.css", "./css/min": "./dist/index.min.css"}, "scripts": {"dev": "postcss src/styles/index.css -o dist/index.css --watch", "build": "node build-css.js", "type-check": "tsc --noEmit"}, "dependencies": {"@onlyrules/shared": "file:../shared", "@tailwindcss/typography": "^0.5.14", "autoprefixer": "10.4.15", "fumadocs-core": "^15.6.9", "fumadocs-mdx": "^11.7.4", "fumadocs-ui": "^15.6.9", "next": "^15.3.5", "postcss": "8.4.30", "react": "18.2.0", "react-dom": "18.2.0", "tailwindcss": "^4", "tailwindcss-animate": "^1.0.7"}, "devDependencies": {"@types/node": "24.1.0", "@types/react": "19.1.8", "typescript": "^5.8.3"}}