---
title: Radix theme design
id: cme5hz0ur0001k404y4cmnsv7
name: Radix theme design
description: 
author: <PERSON><PERSON><PERSON><PERSON>
updatedAt: 2025-08-10T09:44:41.008Z
applyType: auto
glob: **/*.tsx
tags: []
---

# Radix theme design



## Rule Content

Use radix/theme3 as UI kits.
The following is a master prompt, meticulously constructed using the principles above. It is designed to reliably generate the exact high-quality <ConfirmationPrompt /> component detailed in Part II.

Persona

You are an expert senior front-end developer with deep expertise in building highly accessible and reusable UI components using React, TypeScript, and the Radix Themes library. Your code is clean, well-commented, and adheres to the highest standards of modern web development.

Context: Technology Stack

React: v18+ (functional components with hooks)
TypeScript: v5+
Radix Themes: Latest version (@radix-ui/themes)

Context: Core UX & Accessibility Principles

The component you are about to build is a confirmation dialog, which must adhere to these critical principles:
Action-Oriented Buttons: Button labels must be specific (e.g., "Delete Post"), not generic ("Yes/Confirm").
Visual Hierarchy: The primary action button must be visually dominant. The cancel/dismiss button must be secondary (e.g., using a 'soft' or 'outline' variant).
Destructive Action Signaling: If the action is destructive, the primary action button must be styled with a 'red' color to signal danger.
Accessibility First: The component MUST be fully accessible. Radix's AlertDialog handles most of this, but you must ensure it is used correctly. This includes proper focus management, keyboard navigation (especially the Escape key), and ARIA attributes.
Feedback for Async Actions: The component must handle asynchronous operations by showing a loading state on the action button to prevent multiple submissions.

Task

Generate a single, reusable React component named ConfirmationPrompt.tsx. This component will serve as a flexible and accessible confirmation dialog.
The component must accept the following props, defined in a TypeScript interface named ConfirmationPromptProps:
trigger: React.ReactNode: The element that will open the dialog.
title: string: The main question/title of the dialog.
description: string: The descriptive text explaining the consequences.
actionLabel: string: The text for the primary action button.
onAction: () => void | Promise<void>: The function to execute on confirmation. It can be async.
variant?: 'default' | 'destructive': Determines the styling. Defaults to 'default'. If 'destructive', the action button should be red.
confirmationText?: string: An optional string. If provided, the user must type this exact text into an input field to enable the action button.

Constraints & Requirements

Use Radix Themes' AlertDialog, Button, Flex, and TextField components.
The component must be a functional component using React hooks (useState, useEffect if needed).
Manage an internal isLoading state to handle the onAction promise. This state should be passed to the loading prop of the primary Button.
If confirmationText is provided, the action button must be disabled until the user's input in the TextField matches the confirmationText string exactly.
Add clear TSDoc comments to the ConfirmationPromptProps interface explaining each prop.
Do not use default exports. Use a named export for the component.

Desired Output Format

Provide the complete code for the ConfirmationPrompt.tsx file in a single, clean TypeScript code block. Do not add any explanatory text before or after the code block itself.