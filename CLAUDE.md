# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## High-level Architecture

This is a monorepo for the "OnlyRules AI Prompt Management Platform". The project is structured into two main packages:

-   `packages/web`: A Next.js application that serves as the main web frontend. It uses Prisma for database interaction, `better-auth` for authentication, and Lingui for internationalization. The UI is built with Radix UI and Tailwind CSS.
-   `packages/shared`: A package for shared utilities, TypeScript types, constants, and validation schemas that are used across the monorepo.

The project uses `task` as the recommended command runner to simplify development workflows.

## Common Commands

This project uses `task` as a command runner. `npm` scripts are also available.

### Using Task (Recommended)

-   **List all tasks**: `task --list`
-   **Initial setup**: `task setup`
-   **Start development server**: `task dev`
-   **Build for production**: `task build`
-   **Run tests**: `task test:run`
-   **Run linting**: `task lint`
-   **Check code with biome**: `task check`
-   **Open database studio**: `task db:studio`

### Using npm

-   **Start development server**: `npm run dev`
-   **Build for production**: `npm run build`
-   **Run tests**: `npm run test:run`
-   **Run linting**: `npm run lint` or `npm run lint:biome`
-   **Type checking**: `npm run type-check`
-   **Database migrations**: `npm run db:migrate`
